#!/usr/bin/env python3
"""
Test script for MCP integration
"""

import asyncio
import sys
import os

# Add the bot directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'bot'))

from bot.mcp_router import route_to_mcp, should_use_mcp


async def test_mcp_integration():
    """Test the MCP integration with sample queries."""
    
    test_queries = [
        "Can you think step by step about how to solve this math problem: 2x + 5 = 15?",  # Sequential thinking
        "Please analyze this complex problem systematically",  # Sequential thinking
        "Can you list the files in the directory?",  # Filesystem server
        "Please read file example.txt",  # Filesystem server
        "What's the weather like today?",  # Should not trigger MCP
        "Help me reason through this logic puzzle",  # Sequential thinking
        "Simple question about Python",  # Should not trigger MCP
    ]
    
    print("Testing MCP Integration")
    print("=" * 50)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\nTest {i}: {query}")
        print("-" * 40)
        
        # Test if query should use MCP
        should_use = should_use_mcp(query)
        print(f"Should use MCP: {should_use}")
        
        if should_use:
            try:
                # Test routing to MCP
                response = await route_to_mcp(query)
                if response:
                    print(f"MCP Response: {response[:200]}...")
                else:
                    print("MCP returned no response, would fall back to OpenAI")
            except Exception as e:
                print(f"MCP Error: {e}")
                print("Would fall back to OpenAI")
        else:
            print("Would use OpenAI directly")


if __name__ == "__main__":
    asyncio.run(test_mcp_integration())
