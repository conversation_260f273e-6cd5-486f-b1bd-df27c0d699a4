graph TB
%% User Input Layer
    TU[👤 Telegram User] --> TM[📱 Telegram Message]
TM --> TB[🤖 Telegram Bot Handler]

%% Message Processing Entry Point
TB --> MH[📨 message_handle function<br/>ai_message_handle.py]

%% Initial Processing
MH --> REG[👤 register_user_if_not_exists]
REG --> CHK[🔍 Check if previous message<br/>not answered yet]
CHK --> MODE{🎭 Chat Mode?}

MODE -->|artist| IMG[🎨 generate_image_handle]
MODE -->|assistant/other| PROC[⚙️ Continue Processing]

%% MCP Decision Point
PROC --> TIMEOUT[⏰ Handle Dialog Timeout]
