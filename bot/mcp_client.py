"""
MCP (Model Context Protocol) Client Manager
Handles connections to MCP servers and manages communication.
"""

import asyncio
import logging
from typing import Dict, Optional, Any, List
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import config

logger = logging.getLogger(__name__)


class MCPClientManager:
    """Manages MCP server connections and communication."""
    
    def __init__(self):
        self.servers_config = getattr(config, 'mcp_servers', {})
        self.active_connections: Dict[str, Any] = {}
        logger.info(f"🚀 MCP CLIENT: Initialized with {len(self.servers_config)} configured servers: {list(self.servers_config.keys())}")
        for name, config_data in self.servers_config.items():
            logger.info(f"  📋 {name}: {config_data.get('command')} {' '.join(config_data.get('args', []))}")
            logger.info(f"    🎯 Triggers: {config_data.get('triggers', [])}")
        
    async def get_server_for_query(self, query: str) -> Optional[str]:
        """
        Determine which MCP server should handle the query based on trigger keywords.
        Returns server name or None if no server matches.
        """
        logger.debug(f"🔍 MCP CLIENT: Checking {len(self.servers_config)} servers for query triggers")
        query_lower = query.lower()

        for server_name, server_config in self.servers_config.items():
            triggers = server_config.get('triggers', [])
            matched_triggers = [t for t in triggers if t.lower() in query_lower]

            if matched_triggers:
                logger.info(f"✅ MCP CLIENT: Query matched server '{server_name}' with triggers: {matched_triggers}")
                return server_name
            else:
                logger.debug(f"❌ MCP CLIENT: No match for server '{server_name}' (triggers: {triggers})")

        logger.debug("❌ MCP CLIENT: No server matched the query")
        return None
    
    async def call_mcp_server(self, server_name: str, query: str) -> Optional[str]:
        """
        Call the specified MCP server with the query.
        Returns the response or None if failed.
        """
        if server_name not in self.servers_config:
            logger.error(f"💥 MCP CLIENT: Unknown MCP server: {server_name}")
            return None

        logger.info(f"🔌 MCP CLIENT: Connecting to server '{server_name}'...")

        try:
            server_config = self.servers_config[server_name]

            # Create server parameters
            server_params = StdioServerParameters(
                command=server_config['command'],
                args=server_config['args']
            )

            logger.info(f"🚀 MCP CLIENT: Starting server: {server_config['command']} {' '.join(server_config['args'])}")

            # Connect to MCP server and get response
            async with stdio_client(server_params) as (read, write):
                logger.info(f"✅ MCP CLIENT: Connected to {server_name}, initializing session...")

                async with ClientSession(read, write) as session:
                    await session.initialize()
                    logger.info(f"✅ MCP CLIENT: Session initialized for {server_name}")

                    # List available tools
                    tools = await session.list_tools()
                    logger.info(f"🔧 MCP CLIENT: Found {len(tools.tools)} tools: {[t.name for t in tools.tools]}")

                    if not tools.tools:
                        logger.warning(f"⚠️ MCP CLIENT: No tools available in MCP server: {server_name}")
                        return None

                    # Use the first available tool for all servers
                    tool_name = tools.tools[0].name
                    logger.info(f"🔧 MCP CLIENT: Using tool '{tool_name}' for query")

                    # Try different common argument patterns
                    arg_patterns = [
                        {"query": query},
                        {"message": query},
                        {"input": query},
                        {"text": query},
                        {"prompt": query}
                    ]

                    # For sequential thinking, use a more sophisticated approach
                    if tool_name == "sequentialthinking":
                        return await self._handle_sequential_thinking(session, query)

                    # For other tools, add common argument patterns
                    if tool_name != "sequentialthinking":
                        pass  # Continue with existing logic

                    logger.info(f"🔄 MCP CLIENT: Trying {len(arg_patterns)} argument patterns for tool '{tool_name}'")

                    for i, args in enumerate(arg_patterns):
                        try:
                            logger.debug(f"🔄 MCP CLIENT: Attempt {i+1}/{len(arg_patterns)} with args: {list(args.keys())}")
                            result = await session.call_tool(tool_name, args)

                            if result.content and result.content[0].text:
                                response_text = str(result.content[0].text)
                                logger.info(f"✅ MCP CLIENT: Got successful response ({len(response_text)} chars) on attempt {i+1}")
                                return response_text
                            else:
                                logger.debug(f"⚠️ MCP CLIENT: Empty response on attempt {i+1}")
                            break
                        except Exception as e:
                            logger.debug(f"❌ MCP CLIENT: Attempt {i+1} failed with args {list(args.keys())}: {e}")
                            continue

                    logger.warning(f"⚠️ MCP CLIENT: All argument patterns failed for tool '{tool_name}'")
                    return None

        except Exception as e:
            logger.error(f"💥 MCP CLIENT: Error calling MCP server {server_name}: {e}", exc_info=True)
            return None
    



# Global instance
mcp_client = MCPClientManager()
