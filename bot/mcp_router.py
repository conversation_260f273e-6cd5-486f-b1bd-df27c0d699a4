"""
MCP Request Router
Intelligent routing logic to determine which MCP server should handle a request.
"""

import logging
from typing import Optional
from mcp_client import mcp_client

logger = logging.getLogger(__name__)


async def route_to_mcp(message: str) -> Optional[str]:
    """
    Route a user message to the appropriate MCP server.

    Args:
        message: The user's message text

    Returns:
        MCP server response if routed to MCP, None if should use OpenAI
    """
    logger.info(f"🔍 MCP ROUTING: Checking message for MCP routing: '{message[:100]}{'...' if len(message) > 100 else ''}'")

    try:
        # Check if any MCP server should handle this query
        server_name = await mcp_client.get_server_for_query(message)

        if server_name:
            logger.info(f"✅ MCP ROUTING: Message matched server '{server_name}', calling MCP...")
            response = await mcp_client.call_mcp_server(server_name, message)

            if response:
                # Add a subtle indicator that this came from MCP
                server_config = mcp_client.servers_config.get(server_name, {})
                description = server_config.get('description', server_name)
                logger.info(f"✅ MCP SUCCESS: Got response from {server_name} ({len(response)} chars)")
                return f"🧠 *{description}*\n\n{response}"
            else:
                logger.warning(f"⚠️ MCP WARNING: Server {server_name} returned empty response, falling back to OpenAI")
        else:
            logger.info("❌ MCP ROUTING: No MCP server matched, using OpenAI")

        # No MCP server matched or MCP failed, use OpenAI
        return None

    except Exception as e:
        logger.error(f"💥 MCP ERROR: Error in MCP routing: {e}", exc_info=True)
        # Fall back to OpenAI on any error
        return None


def should_use_mcp(message: str) -> bool:
    """
    Quick check if message might benefit from MCP processing.
    Used for early filtering before async operations.
    """
    if not message:
        logger.debug("🔍 MCP CHECK: Empty message, skipping MCP")
        return False

    message_lower = message.lower()

    # Check for all MCP server keywords
    all_mcp_keywords = [
        # Sequential thinking
        "think", "analyze", "reason", "step by step", "complex problem",
        "reasoning", "logic", "solve", "breakdown", "systematic",
        "explain how", "walk me through", "break down", "step-by-step",

        # Memory
        "remember", "recall", "memory", "save this", "store", "knowledge", "learn", "forget",

        # Fetch
        "fetch", "get", "download", "url", "website", "web", "scrape", "content",

        # Time
        "time", "date", "timezone", "when", "clock", "schedule", "calendar"
    ]

    matched_keywords = [kw for kw in all_mcp_keywords if kw in message_lower]
    should_use = len(matched_keywords) > 0

    if should_use:
        logger.info(f"🎯 MCP CHECK: Message contains MCP triggers: {matched_keywords}")
    else:
        logger.debug(f"🔍 MCP CHECK: No MCP triggers found in message")

    return should_use
